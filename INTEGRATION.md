# Re-Shell Integration Guide

This document explains how the different repositories in the Re-Shell ecosystem work together.

## Repository Overview

The Re-Shell ecosystem can be used in two different configurations:

### Monorepo Configuration

The **[re-shell-monorepo](https://github.com/Re-Shell/re-shell)** contains:

1. **Core Framework** (`packages/core`) - Core microfrontend framework
2. **UI Library** (`packages/ui`) - Shared UI components
3. **CLI Tools** (`packages/cli`) - Tools for creating new microfrontends

### Separate Repositories

For production or independent team development, the ecosystem can also be structured as:

1. **[re-shell-monorepo](https://github.com/Re-Shell/re-shell)** - Core framework and tools
2. **[dashboard](https://github.com/Re-Shell/dashboard)** - Dashboard microfrontend
3. **[users](https://github.com/Re-Shell/users)** - Users management microfrontend
4. **[standalone-demo](https://github.com/Re-Shell/standalone-demo)** - Standalone demo application

## Integration Architecture

![Re-Shell Architecture](https://mermaid.ink/img/pako:eNp1kc1qwzAQhF9F7CmFukfDQU5pS0-9lFJyWVtbW6L-LEtOQ8h7V7KTQglZHcT3ze6MNNgHA2LEHn9sDBkKOPMZVgqCFUmjUJEuYDEXM9hkWYENXH0Sib_9mC3g6E2P1-wM3n-TUuDuXFj1Ye1z01ylWbKW8lnKzWaTkA1cXZo-3KarxdOS569PcIg2OLTdoVJr5X2P7EDdETYA2aCAp7dMQqNQyJgxltlwGJCnm_P5TMJzjHOIZPpIo5HXJHvtcX5DQSt6q-2AZAabJqyIZhDIUjRdNwcnS4fULcWACXgDVkdDZR97l2f_JbBQGq8bFqLFXeGsJi7zYcvYYUe1dVrFyqM4oRg4_blfWf4DYiiVdA?type=png)

### Component Interaction

1. **Core Framework (re-shell-monorepo)**
   - Provides the core functionality for loading and managing microfrontends
   - Defines the contract for communication between microfrontends
   - Supplies React components for seamless integration

2. **Microfrontends (dashboard, users)**
   - Built as standalone applications that can be developed and deployed independently
   - Expose a standard interface for mounting/unmounting
   - Register with the shell application through a well-defined contract

3. **Shell Application (standalone-demo)**
   - Acts as the container application that loads and orchestrates microfrontends
   - Provides common layout, navigation, and routing
   - Manages authentication and global state

## Integration Contract

All microfrontends must adhere to the following contract:

```typescript
interface MicrofrontendInterface {
  // Mount the microfrontend to a DOM container
  mount: (containerId: string) => void;

  // Unmount and clean up resources
  unmount: (containerId: string) => void;
}

// Global exports
declare global {
  interface Window {
    [key: string]: MicrofrontendInterface;
  }
}
```

## Development Workflow

1. **Local Development**
   - Run each microfrontend in development mode on a different port
   - Use the standalone demo with proxying to load microfrontends

2. **Deployment**
   - Build each microfrontend as a UMD bundle
   - Deploy each microfrontend to its own location (CDN or server)
   - Configure the shell application to load microfrontends from their respective locations

## Communication Between Microfrontends

Microfrontends can communicate using:

1. **Event Bus** - For broadcasting events that multiple microfrontends might be interested in
2. **Custom Events** - For direct communication between specific microfrontends
3. **Shared State** - For data that needs to be accessed by multiple microfrontends

## Getting Started

You can run the complete system locally using either the monorepo setup or the separate repositories approach.

### Option 1: Using the Monorepo (Recommended for Development)

1. Clone the monorepo:
   ```bash
   git clone https://github.com/Re-Shell/re-shell.git
   cd re-shell
   ```

2. Install dependencies:
   ```bash
   pnpm install
   ```

3. Build packages and start development servers:
   ```bash
   # Build all packages first
   pnpm build

   # Start all development servers
   pnpm dev
   ```

   This will start the shell, dashboard, and users applications in development mode.

### Option 2: Using Separate Repositories

If you prefer to work with separate repositories (useful for production-like setup):

1. Clone all repositories:
   ```bash
   git clone https://github.com/Re-Shell/re-shell.git
   git clone https://github.com/Re-Shell/dashboard.git
   git clone https://github.com/Re-Shell/users.git
   git clone https://github.com/Re-Shell/standalone-demo.git
   ```

2. Install dependencies in each repository:
   ```bash
   cd re-shell && pnpm install
   cd ../dashboard && npm install
   cd ../users && npm install
   cd ../standalone-demo && npm install
   ```

3. Start the development servers in separate terminals:
   ```bash
   # Terminal 1: Build the core library
   cd re-shell && pnpm build

   # Terminal 2: Run the dashboard microfrontend
   cd dashboard && npm run dev

   # Terminal 3: Run the users microfrontend
   cd users && npm run dev

   # Terminal 4: Run the standalone demo
   cd standalone-demo && npm run dev
   ```

4. Open the standalone demo at http://localhost:3000

## Adding New Microfrontends

To add a new microfrontend to your Re-Shell project:

```bash
# From the root of your Re-Shell project
re-shell add my-new-microfrontend
```

This will create a new microfrontend with the necessary structure and configuration.

Options:
- `--team <team>` - Team name (ownership)
- `--org <organization>` - Organization name (default: "re-shell")
- `--template <template>` - Template to use (react, react-ts)
- `--route <route>` - Route path for the microfrontend (default: "/my-new-microfrontend")
- `--port <port>` - Dev server port (default: 5173)

## CLI Commands (v0.2.0)

The Re-Shell CLI now supports the following commands:

- `add` - Add a new microfrontend to your project
- `remove` - Remove a microfrontend from your project
- `list` - List all registered microfrontends
- `build` - Build one or all microfrontends
- `serve` - Serve microfrontends locally for development

## Integration Testing

When making changes to the core framework or microfrontends, test the integration by:

1. Building the modified components
2. Starting the standalone demo
3. Verifying that all microfrontends load and function correctly


## Versioning

- The core framework uses semantic versioning
- Microfrontends specify their required core framework version as a peer dependency
- The shell application ensures compatibility between all components