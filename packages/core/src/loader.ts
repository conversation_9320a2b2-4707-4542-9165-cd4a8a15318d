import { MicrofrontendConfig, LoadMicrofrontendOptions } from './types';

/**
 * Loads a microfrontend script into the DOM
 * 
 * @param config - Configuration for the microfrontend
 * @param options - Options for loading the microfrontend
 * @returns Promise that resolves when the microfrontend is loaded
 */
export const loadMicrofrontend = async (
  config: MicrofrontendConfig,
  options: LoadMicrofrontendOptions = {}
): Promise<void> => {
  const { containerId, url } = config;
  const { async = true, onLoad, onError, timeout = 30000 } = options;
  
  return new Promise<void>((resolve, reject) => {
    // Setup timeout handler
    let timeoutId: number | undefined;
    
    if (timeout > 0) {
      timeoutId = setTimeout(() => {
        const error = new Error(`Timeout of ${timeout}ms exceeded when loading microfrontend from "${url}"`);
        if (onError) onError(error);
        reject(error);
      }, timeout) as unknown as number;
    }
    
    try {
      // Find container element
      const container = document.getElementById(containerId);
      
      if (!container) {
        if (timeoutId) clearTimeout(timeoutId);
        const error = new Error(`Container element with ID "${containerId}" not found`);
        if (onError) onError(error);
        reject(error);
        return;
      }
      
      // Create script element
      const script = document.createElement('script');
      script.src = url;
      script.async = async;
      
      // Set up event handlers
      script.onload = () => {
        if (timeoutId) clearTimeout(timeoutId);
        if (onLoad) onLoad();
        resolve();
      };
      
      script.onerror = () => {
        if (timeoutId) clearTimeout(timeoutId);
        const error = new Error(`Failed to load microfrontend from "${url}"`);
        if (onError) onError(error);
        reject(error);
      };
      
      // Append script to container
      container.appendChild(script);
    } catch (error) {
      if (timeoutId) clearTimeout(timeoutId);
      if (onError) onError(error as Error);
      reject(error);
    }
  });
};

/**
 * Loads multiple microfrontends
 * 
 * @param configs - Array of microfrontend configurations
 * @param options - Options for loading the microfrontends
 * @returns Promise that resolves when all microfrontends are loaded
 */
export const loadMicrofrontends = async (
  configs: MicrofrontendConfig[],
  options: LoadMicrofrontendOptions = {}
): Promise<void> => {
  await Promise.all(
    configs.map(config => loadMicrofrontend(config, options))
  );
};