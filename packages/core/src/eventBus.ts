import { EventData, EventHandler, EventSubscription } from './types';

/**
 * Enhanced event bus for communication between microfrontends
 */
class EventBus {
  private events: Record<string, EventHandler[]> = {};
  private subscriptions: Map<string, EventSubscription> = new Map();
  private eventHistory: EventData[] = [];
  private maxHistorySize = 1000;
  private debugMode = false;
  private namespaces: Set<string> = new Set();

  /**
   * Subscribe to an event with enhanced type safety
   * @param event Event name
   * @param handler Handler function
   * @returns Subscription ID for unsubscribing
   */
  on<T = Record<string, unknown>>(event: string, handler: EventHandler<T>): string {
    if (!this.events[event]) {
      this.events[event] = [];
    }

    const subscriptionId = this.generateSubscriptionId();
    const subscription: EventSubscription = {
      event,
      handler: handler as EventHandler,
      id: subscriptionId,
      timestamp: Date.now(),
    };

    this.events[event].push(handler as EventHandler);
    this.subscriptions.set(subscriptionId, subscription);

    if (this.debugMode) {
      console.log(`[EventBus] Subscribed to "${event}" with ID: ${subscriptionId}`);
    }

    return subscriptionId;
  }

  /**
   * Subscribe to an event once
   * @param event Event name
   * @param handler Handler function
   * @returns Subscription ID
   */
  once<T = Record<string, unknown>>(event: string, handler: EventHandler<T>): string {
    const wrappedHandler = (data: EventData<T>) => {
      handler(data);
      this.off(subscriptionId);
    };

    const subscriptionId = this.on(event, wrappedHandler);
    return subscriptionId;
  }

  /**
   * Unsubscribe from an event
   * @param subscriptionIdOrEvent Subscription ID or event name
   * @param handler Optional handler function (required if using event name)
   */
  off(subscriptionIdOrEvent: string, handler?: EventHandler): void {
    // If it's a subscription ID
    if (this.subscriptions.has(subscriptionIdOrEvent)) {
      const subscription = this.subscriptions.get(subscriptionIdOrEvent)!;
      const eventHandlers = this.events[subscription.event];

      if (eventHandlers) {
        this.events[subscription.event] = eventHandlers.filter(h => h !== subscription.handler);
      }

      this.subscriptions.delete(subscriptionIdOrEvent);

      if (this.debugMode) {
        console.log(
          `[EventBus] Unsubscribed from "${subscription.event}" with ID: ${subscriptionIdOrEvent}`
        );
      }

      return;
    }

    // If it's an event name with handler
    if (handler && this.events[subscriptionIdOrEvent]) {
      this.events[subscriptionIdOrEvent] = this.events[subscriptionIdOrEvent].filter(
        h => h !== handler
      );

      // Remove subscription from map
      for (const [id, subscription] of this.subscriptions.entries()) {
        if (subscription.event === subscriptionIdOrEvent && subscription.handler === handler) {
          this.subscriptions.delete(id);
          break;
        }
      }

      if (this.debugMode) {
        console.log(`[EventBus] Unsubscribed from "${subscriptionIdOrEvent}"`);
      }
    }
  }

  /**
   * Emit an event with enhanced data structure
   * @param event Event name
   * @param payload Event payload
   * @param options Additional options
   */
  emit<T = Record<string, unknown>>(
    event: string,
    payload: T,
    options: {
      source?: string;
      namespace?: string;
      meta?: Record<string, unknown>;
    } = {}
  ): void {
    const eventData: EventData<T> = {
      payload,
      timestamp: Date.now(),
      source: options.source,
      namespace: options.namespace,
      meta: options.meta,
    };

    // Add to history
    this.addToHistory(eventData);

    // Add namespace to set
    if (options.namespace) {
      this.namespaces.add(options.namespace);
    }

    if (this.debugMode) {
      console.log(`[EventBus] Emitting "${event}":`, eventData);
    }

    if (!this.events[event]) return;

    this.events[event].forEach(handler => {
      try {
        handler(eventData);
      } catch (error) {
        console.error(`Error in event handler for "${event}":`, error);
      }
    });
  }

  /**
   * Clear all events or events of a specific type
   * @param event Optional event name
   */
  clear(event?: string): void {
    if (event) {
      this.events[event] = [];

      // Remove subscriptions for this event
      for (const [id, subscription] of this.subscriptions.entries()) {
        if (subscription.event === event) {
          this.subscriptions.delete(id);
        }
      }

      if (this.debugMode) {
        console.log(`[EventBus] Cleared event "${event}"`);
      }
    } else {
      this.events = {};
      this.subscriptions.clear();

      if (this.debugMode) {
        console.log('[EventBus] Cleared all events');
      }
    }
  }

  /**
   * Get all active subscriptions
   */
  getSubscriptions(): EventSubscription[] {
    return Array.from(this.subscriptions.values());
  }

  /**
   * Get event history
   */
  getEventHistory(): EventData[] {
    return [...this.eventHistory];
  }

  /**
   * Clear event history
   */
  clearHistory(): void {
    this.eventHistory = [];

    if (this.debugMode) {
      console.log('[EventBus] Cleared event history');
    }
  }

  /**
   * Enable or disable debug mode
   */
  setDebugMode(enabled: boolean): void {
    this.debugMode = enabled;
    console.log(`[EventBus] Debug mode ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Get all registered namespaces
   */
  getNamespaces(): string[] {
    return Array.from(this.namespaces);
  }

  /**
   * Subscribe to all events in a namespace
   */
  onNamespace<T = Record<string, unknown>>(namespace: string, handler: EventHandler<T>): string {
    const wrappedHandler = (data: EventData<T>) => {
      if (data.namespace === namespace) {
        handler(data);
      }
    };

    return this.on('*', wrappedHandler);
  }

  /**
   * Emit event to all listeners (wildcard)
   */
  broadcast<T = Record<string, unknown>>(
    payload: T,
    options: {
      source?: string;
      namespace?: string;
      meta?: Record<string, unknown>;
    } = {}
  ): void {
    this.emit('*', payload, options);
  }

  /**
   * Generate unique subscription ID
   */
  private generateSubscriptionId(): string {
    return `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Add event to history
   */
  private addToHistory<T = Record<string, unknown>>(eventData: EventData<T>): void {
    this.eventHistory.push(eventData as EventData);

    // Maintain max history size
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.shift();
    }
  }

  /**
   * Set maximum history size
   */
  setMaxHistorySize(size: number): void {
    this.maxHistorySize = size;

    // Trim current history if needed
    while (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.shift();
    }
  }

  /**
   * Get statistics about the event bus
   */
  getStats(): {
    totalEvents: number;
    totalSubscriptions: number;
    eventTypes: string[];
    namespaces: string[];
    historySize: number;
  } {
    return {
      totalEvents: Object.keys(this.events).length,
      totalSubscriptions: this.subscriptions.size,
      eventTypes: Object.keys(this.events),
      namespaces: Array.from(this.namespaces),
      historySize: this.eventHistory.length,
    };
  }
}

// Export singleton instance
export const eventBus = new EventBus();
