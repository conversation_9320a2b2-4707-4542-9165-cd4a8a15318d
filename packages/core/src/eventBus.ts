/**
 * Type definition for event handlers
 */
type EventData = Record<string, unknown>;
type EventHandler = (data: EventData) => void;

/**
 * Simple event bus for communication between microfrontends
 */
class EventBus {
  private events: Record<string, EventHandler[]> = {};

  /**
   * Subscribe to an event
   * @param event Event name
   * @param handler Handler function
   */
  on(event: string, handler: EventHandler): void {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(handler);
  }

  /**
   * Unsubscribe from an event
   * @param event Event name
   * @param handler Handler function
   */
  off(event: string, handler: EventHandler): void {
    if (!this.events[event]) return;
    this.events[event] = this.events[event].filter(h => h !== handler);
  }

  /**
   * Emit an event
   * @param event Event name
   * @param data Event data
   */
  emit(event: string, data: EventData): void {
    if (!this.events[event]) return;
    this.events[event].forEach(handler => {
      try {
        handler(data);
      } catch (error) {
        console.error(`Error in event handler for "${event}":`, error);
      }
    });
  }

  /**
   * Clear all events or events of a specific type
   * @param event Optional event name
   */
  clear(event?: string): void {
    if (event) {
      this.events[event] = [];
    } else {
      this.events = {};
    }
  }
}

// Export singleton instance
export const eventBus = new EventBus();