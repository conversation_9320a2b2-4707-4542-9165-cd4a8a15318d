import { MicrofrontendConfig, MicrofrontendPerformanceMetrics } from './types';
import { eventBus } from './eventBus';

/**
 * Performance monitoring configuration
 */
export interface PerformanceConfig {
  /**
   * Whether to enable performance monitoring
   */
  enabled: boolean;
  
  /**
   * Whether to log metrics to console
   */
  logToConsole?: boolean;
  
  /**
   * Whether to emit performance events
   */
  emitEvents?: boolean;
  
  /**
   * Custom metrics collector
   */
  customCollector?: (metrics: MicrofrontendPerformanceMetrics) => void;
  
  /**
   * Performance thresholds for warnings
   */
  thresholds?: {
    loadTime?: number;
    mountTime?: number;
    bundleSize?: number;
    memoryUsage?: number;
  };
}

/**
 * Performance entry for tracking
 */
interface PerformanceEntry {
  microfrontendId: string;
  startTime: number;
  loadStartTime?: number;
  loadEndTime?: number;
  mountStartTime?: number;
  mountEndTime?: number;
  bundleSize?: number;
  initialMemory?: number;
  finalMemory?: number;
}

/**
 * Performance monitor for microfrontends
 */
export class PerformanceMonitor {
  private config: PerformanceConfig;
  private entries: Map<string, PerformanceEntry> = new Map();
  private metrics: Map<string, MicrofrontendPerformanceMetrics[]> = new Map();

  constructor(config: PerformanceConfig = { enabled: true }) {
    this.config = config;
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<PerformanceConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Start tracking a microfrontend load
   */
  startLoad(microfrontendId: string): void {
    if (!this.config.enabled) return;

    const entry: PerformanceEntry = {
      microfrontendId,
      startTime: performance.now(),
      loadStartTime: performance.now(),
      initialMemory: this.getMemoryUsage()
    };

    this.entries.set(microfrontendId, entry);
  }

  /**
   * Mark load completion
   */
  endLoad(microfrontendId: string, bundleSize?: number): void {
    if (!this.config.enabled) return;

    const entry = this.entries.get(microfrontendId);
    if (!entry) return;

    entry.loadEndTime = performance.now();
    if (bundleSize) {
      entry.bundleSize = bundleSize;
    }
  }

  /**
   * Start tracking mount time
   */
  startMount(microfrontendId: string): void {
    if (!this.config.enabled) return;

    const entry = this.entries.get(microfrontendId);
    if (!entry) return;

    entry.mountStartTime = performance.now();
  }

  /**
   * Mark mount completion and finalize metrics
   */
  endMount(microfrontendId: string): void {
    if (!this.config.enabled) return;

    const entry = this.entries.get(microfrontendId);
    if (!entry) return;

    entry.mountEndTime = performance.now();
    entry.finalMemory = this.getMemoryUsage();

    // Calculate final metrics
    const metrics = this.calculateMetrics(entry);
    this.recordMetrics(microfrontendId, metrics);

    // Clean up entry
    this.entries.delete(microfrontendId);
  }

  /**
   * Calculate performance metrics from entry
   */
  private calculateMetrics(entry: PerformanceEntry): MicrofrontendPerformanceMetrics {
    const loadTime = entry.loadEndTime && entry.loadStartTime 
      ? entry.loadEndTime - entry.loadStartTime 
      : 0;

    const mountTime = entry.mountEndTime && entry.mountStartTime
      ? entry.mountEndTime - entry.mountStartTime
      : 0;

    const memoryUsage = entry.finalMemory && entry.initialMemory
      ? entry.finalMemory - entry.initialMemory
      : undefined;

    return {
      loadTime,
      mountTime,
      bundleSize: entry.bundleSize,
      memoryUsage,
      timestamp: Date.now()
    };
  }

  /**
   * Record metrics for a microfrontend
   */
  private recordMetrics(microfrontendId: string, metrics: MicrofrontendPerformanceMetrics): void {
    // Store metrics
    if (!this.metrics.has(microfrontendId)) {
      this.metrics.set(microfrontendId, []);
    }
    this.metrics.get(microfrontendId)!.push(metrics);

    // Log to console if enabled
    if (this.config.logToConsole) {
      console.group(`[Performance] ${microfrontendId}`);
      console.log(`Load time: ${metrics.loadTime.toFixed(2)}ms`);
      console.log(`Mount time: ${metrics.mountTime.toFixed(2)}ms`);
      if (metrics.bundleSize) {
        console.log(`Bundle size: ${this.formatBytes(metrics.bundleSize)}`);
      }
      if (metrics.memoryUsage) {
        console.log(`Memory usage: ${this.formatBytes(metrics.memoryUsage)}`);
      }
      console.groupEnd();
    }

    // Check thresholds and warn if exceeded
    this.checkThresholds(microfrontendId, metrics);

    // Emit performance event
    if (this.config.emitEvents) {
      eventBus.emit('performance:metrics', {
        microfrontendId,
        metrics
      });
    }

    // Call custom collector
    if (this.config.customCollector) {
      this.config.customCollector(metrics);
    }
  }

  /**
   * Check performance thresholds
   */
  private checkThresholds(microfrontendId: string, metrics: MicrofrontendPerformanceMetrics): void {
    const thresholds = this.config.thresholds;
    if (!thresholds) return;

    const warnings: string[] = [];

    if (thresholds.loadTime && metrics.loadTime > thresholds.loadTime) {
      warnings.push(`Load time (${metrics.loadTime.toFixed(2)}ms) exceeds threshold (${thresholds.loadTime}ms)`);
    }

    if (thresholds.mountTime && metrics.mountTime > thresholds.mountTime) {
      warnings.push(`Mount time (${metrics.mountTime.toFixed(2)}ms) exceeds threshold (${thresholds.mountTime}ms)`);
    }

    if (thresholds.bundleSize && metrics.bundleSize && metrics.bundleSize > thresholds.bundleSize) {
      warnings.push(`Bundle size (${this.formatBytes(metrics.bundleSize)}) exceeds threshold (${this.formatBytes(thresholds.bundleSize)})`);
    }

    if (thresholds.memoryUsage && metrics.memoryUsage && metrics.memoryUsage > thresholds.memoryUsage) {
      warnings.push(`Memory usage (${this.formatBytes(metrics.memoryUsage)}) exceeds threshold (${this.formatBytes(thresholds.memoryUsage)})`);
    }

    if (warnings.length > 0) {
      console.warn(`[Performance Warning] ${microfrontendId}:`);
      warnings.forEach(warning => console.warn(`  - ${warning}`));

      eventBus.emit('performance:threshold-exceeded', {
        microfrontendId,
        warnings,
        metrics
      });
    }
  }

  /**
   * Get memory usage (if available)
   */
  private getMemoryUsage(): number | undefined {
    if (typeof window !== 'undefined' && 'memory' in performance) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return undefined;
  }

  /**
   * Format bytes for display
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get metrics for a specific microfrontend
   */
  getMetrics(microfrontendId: string): MicrofrontendPerformanceMetrics[] {
    return this.metrics.get(microfrontendId) || [];
  }

  /**
   * Get all metrics
   */
  getAllMetrics(): Map<string, MicrofrontendPerformanceMetrics[]> {
    return new Map(this.metrics);
  }

  /**
   * Get average metrics for a microfrontend
   */
  getAverageMetrics(microfrontendId: string): MicrofrontendPerformanceMetrics | null {
    const metrics = this.getMetrics(microfrontendId);
    if (metrics.length === 0) return null;

    const sum = metrics.reduce((acc, metric) => ({
      loadTime: acc.loadTime + metric.loadTime,
      mountTime: acc.mountTime + metric.mountTime,
      bundleSize: (acc.bundleSize || 0) + (metric.bundleSize || 0),
      memoryUsage: (acc.memoryUsage || 0) + (metric.memoryUsage || 0),
      timestamp: Math.max(acc.timestamp, metric.timestamp)
    }), {
      loadTime: 0,
      mountTime: 0,
      bundleSize: 0,
      memoryUsage: 0,
      timestamp: 0
    });

    return {
      loadTime: sum.loadTime / metrics.length,
      mountTime: sum.mountTime / metrics.length,
      bundleSize: sum.bundleSize > 0 ? sum.bundleSize / metrics.length : undefined,
      memoryUsage: sum.memoryUsage > 0 ? sum.memoryUsage / metrics.length : undefined,
      timestamp: sum.timestamp
    };
  }

  /**
   * Clear metrics for a specific microfrontend
   */
  clearMetrics(microfrontendId?: string): void {
    if (microfrontendId) {
      this.metrics.delete(microfrontendId);
    } else {
      this.metrics.clear();
    }
  }

  /**
   * Get performance summary
   */
  getSummary(): {
    totalMicrofrontends: number;
    averageLoadTime: number;
    averageMountTime: number;
    totalBundleSize: number;
    slowestMicrofrontend: { id: string; loadTime: number } | null;
  } {
    const allMetrics = Array.from(this.metrics.entries());
    
    if (allMetrics.length === 0) {
      return {
        totalMicrofrontends: 0,
        averageLoadTime: 0,
        averageMountTime: 0,
        totalBundleSize: 0,
        slowestMicrofrontend: null
      };
    }

    let totalLoadTime = 0;
    let totalMountTime = 0;
    let totalBundleSize = 0;
    let slowestMicrofrontend: { id: string; loadTime: number } | null = null;

    for (const [id, metrics] of allMetrics) {
      const avgMetrics = this.getAverageMetrics(id);
      if (avgMetrics) {
        totalLoadTime += avgMetrics.loadTime;
        totalMountTime += avgMetrics.mountTime;
        
        if (avgMetrics.bundleSize) {
          totalBundleSize += avgMetrics.bundleSize;
        }

        if (!slowestMicrofrontend || avgMetrics.loadTime > slowestMicrofrontend.loadTime) {
          slowestMicrofrontend = { id, loadTime: avgMetrics.loadTime };
        }
      }
    }

    return {
      totalMicrofrontends: allMetrics.length,
      averageLoadTime: totalLoadTime / allMetrics.length,
      averageMountTime: totalMountTime / allMetrics.length,
      totalBundleSize,
      slowestMicrofrontend
    };
  }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitor();

/**
 * Performance utilities
 */
export const PerformanceUtils = {
  /**
   * Measure function execution time
   */
  async measureAsync<T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> {
    const start = performance.now();
    const result = await fn();
    const duration = performance.now() - start;
    return { result, duration };
  },

  /**
   * Measure synchronous function execution time
   */
  measure<T>(fn: () => T): { result: T; duration: number } {
    const start = performance.now();
    const result = fn();
    const duration = performance.now() - start;
    return { result, duration };
  },

  /**
   * Create a performance mark
   */
  mark(name: string): void {
    if (typeof performance !== 'undefined' && performance.mark) {
      performance.mark(name);
    }
  },

  /**
   * Measure between two marks
   */
  measureBetweenMarks(name: string, startMark: string, endMark: string): number | null {
    if (typeof performance !== 'undefined' && performance.measure && performance.getEntriesByName) {
      try {
        performance.measure(name, startMark, endMark);
        const entries = performance.getEntriesByName(name);
        return entries.length > 0 ? entries[0].duration : null;
      } catch (error) {
        console.warn('Failed to measure between marks:', error);
        return null;
      }
    }
    return null;
  }
};
