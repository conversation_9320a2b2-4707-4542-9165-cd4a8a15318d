/**
 * Configuration for a microfrontend
 */
export interface MicrofrontendConfig {
  /**
   * Unique identifier for the microfrontend
   */
  id: string;
  
  /**
   * Name of the microfrontend
   */
  name: string;
  
  /**
   * URL to load the microfrontend from
   */
  url: string;
  
  /**
   * DOM element ID where the microfrontend should be mounted
   */
  containerId: string;
  
  /**
   * Optional route path for the microfrontend
   */
  route?: string;
  
  /**
   * Team that owns this microfrontend
   */
  team?: string;
}

/**
 * Options for loading a microfrontend
 */
export interface LoadMicrofrontendOptions {
  /**
   * Whether to load the microfrontend asynchronously
   * @default true
   */
  async?: boolean;
  
  /**
   * Callback function to run when the microfrontend is loaded
   */
  onLoad?: () => void;
  
  /**
   * Callback function to run if loading the microfrontend fails
   */
  onError?: (error: Error) => void;

  /**
   * Timeout in milliseconds for loading the microfrontend
   * @default 30000
   */
  timeout?: number;
}