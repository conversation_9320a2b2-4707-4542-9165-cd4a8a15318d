import { describe, it, expect, vi } from 'vitest';
import { eventBus } from '../src/eventBus';

describe('Event Bus', () => {
  it('should register event listeners and emit events', () => {
    // Setup
    const mockCallback = vi.fn();
    eventBus.on('test-event', mockCallback);
    
    // Act
    eventBus.emit('test-event', { data: 'test-data' });
    
    // Assert
    expect(mockCallback).toHaveBeenCalledOnce();
    expect(mockCallback).toHaveBeenCalledWith({ data: 'test-data' });
  });
  
  it('should register multiple event listeners for the same event', () => {
    // Setup
    const mockCallback1 = vi.fn();
    const mockCallback2 = vi.fn();
    
    eventBus.on('test-event-multiple', mockCallback1);
    eventBus.on('test-event-multiple', mockCallback2);
    
    // Act
    eventBus.emit('test-event-multiple', { data: 'test-data' });
    
    // Assert
    expect(mockCallback1).toHaveBeenCalledOnce();
    expect(mockCallback2).toHaveBeenCalledOnce();
    expect(mockCallback1).toHaveBeenCalledWith({ data: 'test-data' });
    expect(mockCallback2).toHaveBeenCalledWith({ data: 'test-data' });
  });
  
  it('should not call removed event listeners', () => {
    // Setup
    const mockCallback = vi.fn();
    eventBus.on('test-event-removal', mockCallback);
    
    // Act
    eventBus.emit('test-event-removal', { data: 'before-removal' });
    eventBus.off('test-event-removal', mockCallback);
    eventBus.emit('test-event-removal', { data: 'after-removal' });
    
    // Assert
    expect(mockCallback).toHaveBeenCalledOnce();
    expect(mockCallback).toHaveBeenCalledWith({ data: 'before-removal' });
    expect(mockCallback).not.toHaveBeenCalledWith({ data: 'after-removal' });
  });
  
  it('should not error when emitting event with no listeners', () => {
    // This should not throw an error
    expect(() => {
      eventBus.emit('no-listeners', { data: 'test' });
    }).not.toThrow();
  });
  
  it('should not error when removing nonexistent listener', () => {
    // Setup
    const mockCallback = vi.fn();
    
    // This should not throw an error
    expect(() => {
      eventBus.off('nonexistent-event', mockCallback);
    }).not.toThrow();
  });
});