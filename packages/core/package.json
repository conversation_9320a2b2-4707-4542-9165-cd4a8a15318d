{"name": "@re-shell/core", "version": "0.2.0", "description": "Core package for ReShell microfrontend framework", "main": "dist/re-shell.umd.js", "module": "dist/re-shell.mjs", "types": "dist/index.d.ts", "scripts": {"build": "vite build", "dev": "vite build --watch", "test": "vitest", "lint": "eslint src --ext ts,tsx", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["microfrontend", "react", "shell", "framework"], "author": "Re-Shell Organization", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/Re-Shell/re-shell.git", "directory": "packages/core"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@vitejs/plugin-react": "^4.0.0", "rimraf": "^5.0.0", "typescript": "^5.0.0", "vite": "^4.0.0", "vite-plugin-dts": "^3.0.0", "vitest": "^0.34.3"}}