# Changelog

All notable changes to the `@re-shell/core` package will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.2.0] - 2023-09-20

### Added
- Enhanced microfrontend lifecycle management
- Improved event bus with better type safety
- Added support for nested routes in the routing system
- Integrated with the new test application
- Added performance monitoring utilities

### Changed
- Refactored the ShellProvider component for better extensibility
- Improved error handling and boundary components
- Updated React 18 integration with better concurrent mode support

### Fixed
- Fixed memory leaks in event listeners
- Resolved routing conflicts with nested microfrontends
- Fixed issue with state synchronization between microfrontends

## [0.1.0] - 2023-08-15

### Added
- Initial release of core framework
- Basic microfrontend loading functionality
- Event bus for communication
- Simple routing system
- React integration components