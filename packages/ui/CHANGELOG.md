# Changelog

All notable changes to the `@re-shell/ui` package will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.2.0] - 2023-09-20

### Added
- New Button component with enhanced styling options
- Added comprehensive test suite with React Testing Library
- Theme integration for consistent styling
- Accessibility improvements across all components
- Added support for dark mode

### Changed
- Improved component API for better type safety
- Enhanced styling system for better customization
- Refactored internal structure for better maintainability

### Fixed
- Fixed styling inconsistencies in various components
- Resolved accessibility issues in interactive components
- Fixed responsive layout issues

## [0.1.0] - 2023-08-15

### Added
- Initial release of UI component library
- Basic component set: Button, Card, Layout components
- Simple theming support
- TypeScript definitions