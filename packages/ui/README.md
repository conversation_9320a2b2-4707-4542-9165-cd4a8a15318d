# ReShell UI

Shared UI components for ReShell microfrontend framework.

## Installation

```bash
# npm
npm install @re-shell/ui

# yarn
yarn add @re-shell/ui

# pnpm
pnpm add @re-shell/ui
```

## Usage

```jsx
import { <PERSON><PERSON>, <PERSON> } from '@re-shell/ui';

function MyComponent() {
  return (
    <Card>
      <h2>Hello World</h2>
      <Button>Click Me</Button>
    </Card>
  );
}
```

## Components

- Button
- Card
- Layout
- Navigation
- Form elements