{"name": "@re-shell/ui", "version": "0.2.0", "description": "Shared UI components for ReShell microfrontend framework", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "vite build --config vite.config.ts", "dev": "vite build --watch --config vite.config.ts", "test": "vitest", "lint": "eslint src --ext ts,tsx", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["microfrontend", "react", "ui", "components"], "author": "Re-Shell Organization", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/Re-Shell/re-shell.git", "directory": "packages/ui"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@vitejs/plugin-react": "^4.0.0", "jsdom": "^26.1.0", "react": "^18.0.0", "react-dom": "^18.0.0", "rimraf": "^5.0.0", "typescript": "^5.0.0", "vite": "^4.0.0", "vite-plugin-dts": "^3.0.0", "vitest": "^0.34.3"}}