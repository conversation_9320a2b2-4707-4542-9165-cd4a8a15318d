{"name": "@re-shell/cli", "version": "0.2.1", "description": "CLI tool for Re-Shell microfrontend framework", "main": "dist/index.js", "files": ["dist/index.js", "dist/index.d.ts", "dist/commands", "README.md"], "bin": {"re-shell": "dist/index.js"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:unit": "vitest run tests/unit", "test:integration": "vitest run tests/integration", "test:e2e": "vitest run tests/e2e", "lint": "eslint src --ext ts", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm run build"}, "keywords": ["microfrontend", "react", "re-shell", "cli", "framework", "micro-frontend"], "author": "Re-Shell Organization", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/Re-Shell/re-shell.git", "directory": "packages/cli"}, "publishConfig": {"access": "public"}, "engines": {"node": ">=14.0.0"}, "dependencies": {"commander": "^11.0.0", "fs-extra": "^11.0.0", "prompts": "^2.0.0", "chalk": "^4.1.2", "ora": "^5.4.1", "semver": "^7.5.4", "yaml": "^2.3.2", "glob": "^10.3.3"}, "devDependencies": {"@types/fs-extra": "^11.0.0", "@types/node": "^20.0.0", "@types/prompts": "^2.0.0", "@types/uuid": "^9.0.0", "@types/semver": "^7.5.0", "@types/glob": "^8.1.0", "rimraf": "^5.0.0", "typescript": "^5.0.0", "uuid": "^9.0.0", "vitest": "^0.34.3"}}